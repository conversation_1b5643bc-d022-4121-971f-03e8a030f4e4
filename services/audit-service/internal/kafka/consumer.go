package kafka

import (
	"audit-service/internal/models"
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/segmentio/kafka-go"
)

type AuditService interface {
	CreateAuditLog(req *models.CreateAuditLogRequest) error
}

type Consumer struct {
	reader       *kafka.Reader
	auditService AuditService
}

func NewConsumer(brokers []string, topics []string, groupID string, auditService AuditService) *Consumer {
	reader := kafka.NewReader(kafka.ReaderConfig{
		Brokers:        brokers,
		GroupID:        groupID,
		GroupTopics:    topics,
		MinBytes:       10e3, // 10KB
		MaxBytes:       10e6, // 10MB
		CommitInterval: time.Second,
		StartOffset:    kafka.LastOffset,
	})

	return &Consumer{
		reader:       reader,
		auditService: auditService,
	}
}

func (c *Consumer) Start(ctx context.Context) error {
	log.Println("Starting Kafka consumer...")

	for {
		select {
		case <-ctx.Done():
			log.Println("Stopping Kafka consumer...")
			return c.reader.Close()
		default:
			message, err := c.reader.ReadMessage(ctx)
			if err != nil {
				log.Printf("Error reading message: %v", err)
				continue
			}

			if err := c.processMessage(message); err != nil {
				log.Printf("Error processing message: %v", err)
			}
		}
	}
}

func (c *Consumer) processMessage(message kafka.Message) error {
	log.Printf("Received message from topic %s: %s", message.Topic, string(message.Value))

	var event models.KafkaEvent
	if err := json.Unmarshal(message.Value, &event); err != nil {
		log.Printf("Error unmarshaling event: %v", err)
		return err
	}

	// Convert the event to an audit log
	auditLog, err := c.convertEventToAuditLog(&event, message.Topic)
	if err != nil {
		log.Printf("Error converting event to audit log: %v", err)
		return err
	}

	// Create the audit log
	if err := c.auditService.CreateAuditLog(auditLog); err != nil {
		log.Printf("Error creating audit log: %v", err)
		return err
	}

	log.Printf("Successfully processed event: %s", event.EventType)
	return nil
}

func (c *Consumer) convertEventToAuditLog(event *models.KafkaEvent, topic string) (*models.CreateAuditLogRequest, error) {
	auditLog := &models.CreateAuditLogRequest{
		EventType: event.EventType,
		Action:    c.getActionFromEventType(event.EventType),
		Details:   event.Details,
	}

	// Convert user_id to uint if it exists
	if event.UserID != nil {
		switch v := event.UserID.(type) {
		case float64:
			userID := uint(v)
			auditLog.UserID = &userID
		case int:
			userID := uint(v)
			auditLog.UserID = &userID
		case uint:
			auditLog.UserID = &v
		}
	}

	// Set resource type based on topic
	resourceType := c.getResourceTypeFromTopic(topic)
	if resourceType != "" {
		auditLog.ResourceType = &resourceType
	}

	// Add email to details if available
	if event.Email != "" {
		if auditLog.Details == nil {
			auditLog.Details = make(map[string]interface{})
		}
		auditLog.Details["email"] = event.Email
	}

	return auditLog, nil
}

func (c *Consumer) getActionFromEventType(eventType string) string {
	switch eventType {
	case "user_registered":
		return "CREATE"
	case "user_logged_in":
		return "LOGIN"
	case "user_logged_out":
		return "LOGOUT"
	case "profile_updated":
		return "UPDATE"
	case "address_created":
		return "CREATE"
	case "address_updated":
		return "UPDATE"
	case "address_deleted":
		return "DELETE"
	case "preferences_updated":
		return "UPDATE"
	case "product_created":
		return "CREATE"
	case "product_updated":
		return "UPDATE"
	case "product_deleted":
		return "DELETE"
	case "cart_item_added":
		return "CREATE"
	case "cart_item_updated":
		return "UPDATE"
	case "cart_item_removed":
		return "DELETE"
	case "order_created":
		return "CREATE"
	default:
		return "UNKNOWN"
	}
}

func (c *Consumer) getResourceTypeFromTopic(topic string) string {
	switch topic {
	case "auth.events":
		return "auth"
	case "user.events":
		return "user"
	case "shop.events":
		return "shop"
	default:
		return ""
	}
}

func (c *Consumer) Close() error {
	return c.reader.Close()
}
